// 测试API - 验证PocketBase Hook功能

// 简单的测试端点
routerAdd("GET", "/api/test", (c) => {
  console.log('收到测试请求');
  
  return c.json(200, {
    success: true,
    message: "PocketBase Hook 工作正常",
    timestamp: new Date().toISOString()
  });
});

// 测试认证状态
routerAdd("GET", "/api/test/auth", (c) => {
  console.log('收到认证测试请求');
  
  const authRecord = c.get("authRecord");
  
  return c.json(200, {
    success: true,
    authenticated: !!authRecord,
    userId: authRecord ? authRecord.id : null,
    userEmail: authRecord ? authRecord.get("email") : null
  });
});

// 测试集合是否存在
routerAdd("GET", "/api/test/collections", (c) => {
  console.log('收到集合测试请求');

  return c.json(200, {
    success: true,
    message: "集合测试功能暂时禁用，请使用PocketBase Admin界面检查集合"
  });
});

console.log('测试API已加载');
