import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart';
import 'package:uuid/uuid.dart';
import '../models/fishing_spot.dart';
import '../services/service_locator.dart';

/// 分屏添加钓点组件
class SplitScreenAddSpot extends StatefulWidget {
  /// 钓点位置
  final LatLng location;

  /// 位置更新回调
  final Function(LatLng) onLocationChanged;

  /// 关闭回调
  final VoidCallback onClose;

  /// 成功添加钓点回调
  final Function(FishingSpot) onSpotAdded;

  const SplitScreenAddSpot({
    super.key,
    required this.location,
    required this.onLocationChanged,
    required this.onClose,
    required this.onSpotAdded,
  });

  @override
  State<SplitScreenAddSpot> createState() => _SplitScreenAddSpotState();
}

class _SplitScreenAddSpotState extends State<SplitScreenAddSpot> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _fishTypesController = TextEditingController();

  String _selectedSpotType = '淡水';
  final List<String> _spotTypes = ['淡水', '海水', '池塘', '水库', '河流', '湖泊'];

  List<File> _selectedImages = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _fishTypesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题和关闭按钮
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          '添加钓点',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: widget.onClose,
                        ),
                      ],
                    ),

                    // 位置信息
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.location_on, color: Colors.blue),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '位置: ${widget.location.latitude.toStringAsFixed(6)}, ${widget.location.longitude.toStringAsFixed(6)}',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 钓点名称
                            TextFormField(
                              controller: _nameController,
                              decoration: const InputDecoration(
                                labelText: '钓点名称 *',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return '请输入钓点名称';
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            // 钓点描述
                            TextFormField(
                              controller: _descriptionController,
                              decoration: const InputDecoration(
                                labelText: '钓点描述',
                                border: OutlineInputBorder(),
                              ),
                              maxLines: 3,
                            ),

                            const SizedBox(height: 16),

                            // 钓点类型
                            DropdownButtonFormField<String>(
                              value: _selectedSpotType,
                              decoration: const InputDecoration(
                                labelText: '钓点类型',
                                border: OutlineInputBorder(),
                              ),
                              items:
                                  _spotTypes.map((type) {
                                    return DropdownMenuItem(
                                      value: type,
                                      child: Text(type),
                                    );
                                  }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedSpotType = value!;
                                });
                              },
                            ),

                            const SizedBox(height: 16),

                            // 鱼类信息
                            TextFormField(
                              controller: _fishTypesController,
                              decoration: const InputDecoration(
                                labelText: '鱼类信息',
                                hintText: '例如：鲫鱼、鲤鱼、草鱼',
                                border: OutlineInputBorder(),
                              ),
                            ),

                            const SizedBox(height: 16),

                            // 照片上传
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  '钓点照片:',
                                  style: TextStyle(fontSize: 16),
                                ),
                                const SizedBox(height: 8),
                                SizedBox(
                                  height: 80,
                                  child: ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    itemCount:
                                        _selectedImages.length +
                                        1, // +1 for the add button
                                    itemBuilder: (context, index) {
                                      if (index == _selectedImages.length) {
                                        // 添加照片的方框
                                        return Container(
                                          width: 80,
                                          height: 80,
                                          margin: const EdgeInsets.only(
                                            right: 8,
                                          ),
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                              color: Colors.grey.shade400,
                                              width: 2,
                                              style: BorderStyle.solid,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                            color: Colors.grey.shade100,
                                          ),
                                          child: InkWell(
                                            onTap: _pickImage,
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                            child: const Icon(
                                              Icons.add,
                                              size: 32,
                                              color: Colors.grey,
                                            ),
                                          ),
                                        );
                                      } else {
                                        // 已选择的照片缩略图
                                        return Container(
                                          width: 80,
                                          height: 80,
                                          margin: const EdgeInsets.only(
                                            right: 8,
                                          ),
                                          child: Stack(
                                            children: [
                                              ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                child: Image.file(
                                                  _selectedImages[index],
                                                  width: 80,
                                                  height: 80,
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                              Positioned(
                                                top: 4,
                                                right: 4,
                                                child: GestureDetector(
                                                  onTap:
                                                      () => _removeImage(index),
                                                  child: Container(
                                                    width: 20,
                                                    height: 20,
                                                    decoration:
                                                        const BoxDecoration(
                                                          color: Colors.red,
                                                          shape:
                                                              BoxShape.circle,
                                                        ),
                                                    child: const Icon(
                                                      Icons.close,
                                                      color: Colors.white,
                                                      size: 14,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 24),

                            // 发布按钮
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: _isLoading ? null : _handleSubmit,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      Theme.of(context).primaryColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 16,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: const Text(
                                  '发布钓点',
                                  style: TextStyle(fontSize: 16),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  // 选择图片
  Future<void> _pickImage() async {
    // 显示选择对话框：相机或图库
    final ImageSource? source = await showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择图片来源'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('拍照'),
                onTap: () => Navigator.of(context).pop(ImageSource.camera),
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('从图库选择'),
                onTap: () => Navigator.of(context).pop(ImageSource.gallery),
              ),
            ],
          ),
        );
      },
    );

    if (source != null) {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: source);

      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
      }
    }
  }

  // 移除图片
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  // 处理提交
  Future<void> _handleSubmit() async {
    if (!Services.auth.isLoggedIn) {
      // 跳转到登录页面，等待登录结果
      final result = await Navigator.pushNamed(context, '/login');
      // 如果登录成功，继续发布流程
      if (result == true && Services.auth.isLoggedIn) {
        // 登录成功后，递归调用自己继续发布
        await _handleSubmit();
      }
      return;
    }

    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user = Services.auth.currentUser;

      // 创建钓点对象
      final spot = FishingSpot(
        id: const Uuid().v4(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        latitude: widget.location.latitude,
        longitude: widget.location.longitude,
        userId: user?.id ?? '',
        spotType: _selectedSpotType,
        fishTypes: _fishTypesController.text.trim(),
        created: DateTime.now(),
        updated: DateTime.now(),
      );

      // 添加钓点
      final addedSpot = await Services.fishingSpot.addSpot(spot);

      if (addedSpot != null) {
        // 添加成功，调用回调
        widget.onSpotAdded(addedSpot);

        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('钓点发布成功！')));
          widget.onClose();
        }
      } else {
        throw Exception('添加钓点失败');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('发布失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
