import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// 天地图逆地理编码服务
class TianDiTuService {
  static const String _baseUrl = 'http://api.tianditu.gov.cn/geocoder';
  // TODO: 替换为实际的天地图密钥
  static const String _appKey = '您的天地图密钥';
  
  final Dio _dio = Dio(BaseOptions(
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
    contentType: Headers.formUrlEncodedContentType,
  ));

  /// 逆地理编码：根据经纬度获取地址信息
  /// [longitude] 经度
  /// [latitude] 纬度
  /// 返回地址信息，如果失败返回null
  Future<Map<String, dynamic>?> reverseGeocode(double longitude, double latitude) async {
    try {
      final String postStr = json.encode({
        'lon': longitude.toString(),
        'lat': latitude.toString(),
        'ver': '1'
      });
      
      final String url = '$_baseUrl?postStr=$postStr&type=geocode&tk=$_appKey';
      
      debugPrint('天地图API请求: $url');
      
      final response = await _dio.get(url);
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.data);
        
        debugPrint('天地图API响应: $data');
        
        if (data['status'] == '0') {
          return data['result'];
        } else {
          debugPrint('天地图API错误: ${data['msg']}');
          return null;
        }
      } else {
        debugPrint('HTTP请求失败: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('逆地理编码请求异常: $e');
      return null;
    }
  }

  /// 获取格式化的地址字符串
  Future<String?> getFormattedAddress(double longitude, double latitude) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['formatted_address'];
  }

  /// 获取道路名称
  Future<String?> getRoadName(double longitude, double latitude) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['road'];
  }

  /// 获取POI信息
  Future<String?> getPOIName(double longitude, double latitude) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['poi'];
  }

  /// 获取城市信息
  Future<String?> getCityName(double longitude, double latitude) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['city'];
  }

  /// 获取最佳的钓点名称
  /// 优先级：POI > 道路 > 格式化地址的最后一部分
  Future<String?> getBestLocationName(double longitude, double latitude) async {
    try {
      final result = await reverseGeocode(longitude, latitude);
      if (result == null) return null;

      final addressComponent = result['addressComponent'];
      if (addressComponent == null) return null;

      // 优先使用POI名称
      String? poi = addressComponent['poi'];
      if (poi != null && poi.trim().isNotEmpty) {
        return poi.trim();
      }

      // 其次使用道路名称
      String? road = addressComponent['road'];
      if (road != null && road.trim().isNotEmpty) {
        return road.trim();
      }

      // 最后使用格式化地址的最后一部分
      String? formattedAddress = result['formatted_address'];
      if (formattedAddress != null && formattedAddress.trim().isNotEmpty) {
        // 尝试提取地址中的关键部分
        List<String> parts = formattedAddress.split('');
        if (parts.isNotEmpty) {
          // 取最后几个有意义的部分
          String lastPart = parts.last.trim();
          if (lastPart.isNotEmpty) {
            return lastPart;
          }
        }
        return formattedAddress.trim();
      }

      return null;
    } catch (e) {
      debugPrint('获取最佳位置名称失败: $e');
      return null;
    }
  }

  /// 获取完整的地址组件信息
  Future<AddressComponent?> getAddressComponent(double longitude, double latitude) async {
    final result = await reverseGeocode(longitude, latitude);
    if (result != null && result['addressComponent'] != null) {
      return AddressComponent.fromJson(result['addressComponent']);
    }
    return null;
  }
}

/// 地址组件数据模型
class AddressComponent {
  final String? address;
  final int? addressDistance;
  final String? addressPosition;
  final String? city;
  final String? poi;
  final int? poiDistance;
  final String? poiPosition;
  final String? road;
  final int? roadDistance;

  AddressComponent({
    this.address,
    this.addressDistance,
    this.addressPosition,
    this.city,
    this.poi,
    this.poiDistance,
    this.poiPosition,
    this.road,
    this.roadDistance,
  });

  factory AddressComponent.fromJson(Map<String, dynamic> json) {
    return AddressComponent(
      address: json['address'],
      addressDistance: json['address_distance'],
      addressPosition: json['address_position'],
      city: json['city'],
      poi: json['poi'],
      poiDistance: json['poi_distance'],
      poiPosition: json['poi_position'],
      road: json['road'],
      roadDistance: json['road_distance'],
    );
  }

  @override
  String toString() {
    return 'AddressComponent{address: $address, city: $city, poi: $poi, road: $road}';
  }
}
