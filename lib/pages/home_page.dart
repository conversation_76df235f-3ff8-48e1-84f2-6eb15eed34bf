import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:async';
import '../services/service_locator.dart';
import '../models/fishing_spot.dart';

import '../widgets/dev_menu.dart';
import '../widgets/split_screen_add_spot.dart';

const String tiandituKey = "23fee0544e2033fa1dbc0d6ae7bf30ad";

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  // 天地图密钥，需要在天地图官网申请
  final MapController mapController = MapController();
  // 地图类型：true为矢量图，false为卫星图
  bool isVectorMap = false;
  // 是否显示注记层
  bool showAnnotationLayer = true;

  // 服务 - 使用新的服务架构
  // 通过Services便捷访问器访问服务

  // 数据
  List<FishingSpot> _spots = [];
  LatLng _userLocation = const LatLng(39.9042, 116.4074); // 默认北京位置
  bool _isLoading = true;

  final GlobalKey _mapKey = GlobalKey();

  // 分屏添加钓点模式状态
  bool _isSplitScreenMode = false;
  LatLng _centerMarkerLocation = const LatLng(39.9042, 116.4074);
  String? _suggestedSpotName; // 建议的钓点名称

  // 动画控制器
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  // 定时位置更新
  Timer? _locationUpdateTimer;
  final bool _enablePeriodicLocationUpdate = true;
  static const Duration _locationUpdateInterval = Duration(seconds: 10);

  // 位置重置按钮状态
  bool _isLocationResetting = false;

  // 位置监听订阅
  StreamSubscription<LatLng>? _locationSubscription;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _initializeData();

    // 监听地图移动，加载范围内的钓点
    mapController.mapEventStream.listen((event) {
      if (event is MapEventMoveEnd) {
        _loadSpotsInBounds();
      }
    });
  }

  @override
  void dispose() {
    // 清理定时器和监听器
    _locationUpdateTimer?.cancel();
    _locationSubscription?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  // 初始化数据
  Future<void> _initializeData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 静默尝试登录（不显示任何错误提示）
      try {
        if (!Services.auth.isLoggedIn) {
          await Services.auth.initialize();
        }
      } catch (e) {
        // 静默处理登录失败，不显示任何提示
        debugPrint('静默登录失败: $e');
      }

      // 初始化瓦片缓存服务
      await Services.cache.initialize();

      // 获取本地存储的位置或默认位置
      final initialLocation = Services.location.getCurrentLocation();

      setState(() {
        _userLocation = initialLocation;
        _isLoading = false;
      });

      // 移动地图到用户位置（使用较低的缩放级别以便用户看到更多内容）
      mapController.move(_userLocation, 15.0);

      // 立即加载范围内的钓点，不需要延迟
      _loadSpotsInBounds();

      // 异步获取最新位置（不阻塞UI）
      Services.location
          .requestLocationUpdate()
          .then((newLocation) {
            if (mounted) {
              setState(() {
                _userLocation = newLocation;
              });

              // 只有当位置变化较大时才移动地图（超过100米）
              final distance = Services.location.calculateDistance(
                _userLocation,
                newLocation,
              );
              if (distance > 0.1) {
                // 0.1公里 = 100米
                mapController.move(newLocation, mapController.camera.zoom);
                _loadSpotsInBounds(); // 重新加载新位置范围内的钓点
              }
            }
          })
          .catchError((e) {
            debugPrint('异步获取位置失败: $e');
          });

      // 启动定时位置更新
      _startPeriodicLocationUpdate();

      // 启动位置监听
      _startLocationListening();
    } catch (e) {
      debugPrint('初始化数据失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 启动定时位置更新
  void _startPeriodicLocationUpdate() {
    if (!_enablePeriodicLocationUpdate) return;

    _locationUpdateTimer = Timer.periodic(_locationUpdateInterval, (
      timer,
    ) async {
      if (!mounted) {
        timer.cancel();
        return;
      }

      try {
        final newLocation = await Services.location.requestLocationUpdate();
        if (mounted) {
          final distance = Services.location.calculateDistance(
            _userLocation,
            newLocation,
          );

          // 只有当位置变化超过50米时才更新UI
          if (distance > 0.05) {
            setState(() {
              _userLocation = newLocation;
            });

            // 如果位置变化较大（超过500米），重新加载钓点
            if (distance > 0.5) {
              _loadSpotsInBounds();
            }
          }
        }
      } catch (e) {
        debugPrint('定时位置更新失败: $e');
      }
    });
  }

  // 启动位置监听
  void _startLocationListening() {
    // 启动位置服务的实时监听
    Services.location.startLocationTracking();

    // 监听位置变化流
    _locationSubscription = Services.location.locationStream.listen(
      (LatLng newLocation) {
        if (mounted) {
          final distance = Services.location.calculateDistance(
            _userLocation,
            newLocation,
          );

          // 只有当位置变化超过20米时才更新UI
          if (distance > 0.02) {
            setState(() {
              _userLocation = newLocation;
            });

            // 如果位置变化较大（超过200米），重新加载钓点
            if (distance > 0.2) {
              _loadSpotsInBounds();
            }
          }
        }
      },
      onError: (error) {
        debugPrint('位置监听错误: $error');
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: const [
          DevMenu(), // 开发者菜单（仅在开发模式下显示）
        ],
      ),
      extendBodyBehindAppBar: true, // 让body延伸到AppBar后面
      body:
          _isSplitScreenMode ? _buildSplitScreenLayout() : _buildNormalLayout(),
      // 右下角地图控制按钮（分屏模式下隐藏）
      floatingActionButton:
          _isSplitScreenMode
              ? null
              : Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  FloatingActionButton(
                    onPressed: () {
                      // 切换地图类型
                      setState(() {
                        isVectorMap = !isVectorMap;
                      });
                    },
                    heroTag: 'switchMap',
                    tooltip: '切换地图类型',
                    backgroundColor: Colors.white,
                    child: FaIcon(
                      isVectorMap
                          ? FontAwesomeIcons.map
                          : FontAwesomeIcons.satellite,
                    ),
                  ),
                  const SizedBox(height: 16),
                  FloatingActionButton(
                    onPressed: () {
                      // 切换注记层显示
                      setState(() {
                        showAnnotationLayer = !showAnnotationLayer;
                      });
                    },
                    heroTag: 'toggleAnnotation',
                    tooltip: '切换注记层',
                    backgroundColor: Colors.white,
                    child: FaIcon(
                      showAnnotationLayer
                          ? FontAwesomeIcons.layerGroup
                          : FontAwesomeIcons.square,
                    ),
                  ),
                  const SizedBox(height: 16),
                  FloatingActionButton(
                    onPressed:
                        _isLocationResetting
                            ? null
                            : () {
                              // 重置地图位置并获取最新位置
                              _updateCurrentLocation();
                            },
                    heroTag: 'resetLocation',
                    tooltip: '重置位置',
                    backgroundColor:
                        _isLocationResetting
                            ? Colors.grey
                            : Colors.white,
                    child:
                        _isLocationResetting
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                            : const FaIcon(FontAwesomeIcons.locationCrosshairs),
                  ),
                  const SizedBox(height: 16),
                  FloatingActionButton(
                    onPressed: () {
                      // 切换到分屏添加钓点模式
                      _toggleSplitScreenModeWithLocationName();
                    },
                    heroTag: 'addSpot',
                    tooltip: '添加钓点',
                    backgroundColor:
                        _isSplitScreenMode
                            ? Colors.red
                            : Colors.white,
                    child: FaIcon(
                      _isSplitScreenMode
                          ? FontAwesomeIcons.xmark
                          : FontAwesomeIcons.plus,
                    ),
                  ),
                ],
              ),
    );
  }

  // 构建正常布局
  Widget _buildNormalLayout() {
    return Stack(
      children: [
        _isLoading
            ? const Center(child: CircularProgressIndicator())
            : FlutterMap(
              key: _mapKey,
              mapController: mapController,
              options: MapOptions(
                initialCenter: _userLocation,
                initialZoom: 16.0,
                minZoom: 1, // 设置最小缩放级别
                maxZoom: 18.0, // 设置最大缩放级别
                // 禁用旋转功能
                keepAlive: true,
              ),
              children: [
                // 底图层
                TileLayer(
                  urlTemplate:
                      isVectorMap
                          ? "https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}"
                          : "https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}",
                  additionalOptions: {'k': tiandituKey},
                  subdomains: const ['0', '1', '2', '3', '4', '5', '6', '7'],
                  // 使用带缓存的瓦片提供者提高性能
                  tileProvider: Services.cache.createCachedTileProvider(),
                ),
                // 注记层
                if (showAnnotationLayer)
                  TileLayer(
                    urlTemplate:
                        isVectorMap
                            ? "https://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}"
                            : "https://t{s}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}",
                    additionalOptions: {'k': tiandituKey},
                    subdomains: const ['0', '1', '2', '3', '4', '5', '6', '7'],
                    // 使用带缓存的瓦片提供者提高性能
                    tileProvider: Services.cache.createCachedTileProvider(),
                  ),
                // 添加钓点标记
                MarkerLayer(
                  markers: [
                    // 用户当前位置标记
                    Marker(
                      point: _userLocation,
                      width: 60,
                      height: 60,
                      // 确保标记点始终保持竖直
                      rotate: true,
                      child: const Column(
                        children: [
                          FaIcon(
                            FontAwesomeIcons.streetView,
                            color: Color.fromARGB(255, 0, 62, 247),
                            size: 30,
                          ),
                        ],
                      ),
                    ),
                    // 钓点标记
                    for (final spot in _spots)
                      Marker(
                        point: spot.location,
                        width: 60,
                        height: 60,
                        // 确保标记点始终保持竖直
                        rotate: true,
                        child: GestureDetector(
                          onTap: () => _showSpotDetails(spot),
                          child: const Column(
                            children: [
                              FaIcon(
                                FontAwesomeIcons.fishFins,
                                color: Color.fromARGB(255, 253, 59, 59),
                                size: 30,
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
      ],
    );
  }

  // 构建分屏布局
  Widget _buildSplitScreenLayout() {
    return AnimatedBuilder(
      animation: _slideAnimation,
      builder: (context, child) {
        return Column(
          children: [
            // 上半部分：地图
            Expanded(
              flex: 1,
              child: Stack(
                children: [
                  FlutterMap(
                    key: _mapKey,
                    mapController: mapController,
                    options: MapOptions(
                      initialCenter: _userLocation,
                      initialZoom: 16.0,
                      minZoom: 1,
                      maxZoom: 18.0,
                      keepAlive: true,
                      // 地图移动时更新中心标记位置
                      onPositionChanged: (position, hasGesture) {
                        if (hasGesture) {
                          setState(() {
                            _centerMarkerLocation = mapController.camera.center;
                          });
                        }
                      },
                    ),
                    children: [
                      // 底图层
                      TileLayer(
                        urlTemplate:
                            isVectorMap
                                ? 'https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=$tiandituKey'
                                : 'https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=$tiandituKey',
                        subdomains: const [
                          '0',
                          '1',
                          '2',
                          '3',
                          '4',
                          '5',
                          '6',
                          '7',
                        ],
                        userAgentPackageName: 'com.example.fishing_app',
                      ),

                      // 注记层（仅在矢量图模式下显示）
                      if (isVectorMap && showAnnotationLayer)
                        TileLayer(
                          urlTemplate:
                              'https://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=$tiandituKey',
                          subdomains: const [
                            '0',
                            '1',
                            '2',
                            '3',
                            '4',
                            '5',
                            '6',
                            '7',
                          ],
                          userAgentPackageName: 'com.example.fishing_app',
                        ),

                      // 注记层（仅在卫星图模式下显示）
                      if (!isVectorMap && showAnnotationLayer)
                        TileLayer(
                          urlTemplate:
                              'https://t{s}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=$tiandituKey',
                          subdomains: const [
                            '0',
                            '1',
                            '2',
                            '3',
                            '4',
                            '5',
                            '6',
                            '7',
                          ],
                          userAgentPackageName: 'com.example.fishing_app',
                        ),

                      // 添加钓点标记
                      MarkerLayer(
                        markers: [
                          // 用户当前位置标记
                          Marker(
                            point: _userLocation,
                            width: 60,
                            height: 60,
                            rotate: true,
                            child: const Column(
                              children: [
                                FaIcon(
                                  FontAwesomeIcons.streetView,
                                  color: Color.fromARGB(255, 0, 62, 247),
                                  size: 30,
                                ),
                              ],
                            ),
                          ),
                          // 钓点标记
                          for (final spot in _spots)
                            Marker(
                              point: spot.location,
                              width: 60,
                              height: 60,
                              rotate: true,
                              child: GestureDetector(
                                onTap: () => _showSpotDetails(spot),
                                child: const Column(
                                  children: [
                                    FaIcon(
                                      FontAwesomeIcons.fishFins,
                                      color: Color.fromARGB(255, 253, 59, 59),
                                      size: 30,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),

                  // 中心标记
                  Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const FaIcon(
                          FontAwesomeIcons.locationDot,
                          color: Colors.red,
                          size: 40,
                        ),
                        Container(height: 20, width: 2, color: Colors.red),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // 下半部分：添加钓点表单（带动画）
            Transform.translate(
              offset: Offset(
                0,
                _slideAnimation.value *
                    MediaQuery.of(context).size.height *
                    0.5,
              ),
              child: SplitScreenAddSpot(
                location: _centerMarkerLocation,
                suggestedName: _suggestedSpotName,
                onLocationChanged: (newLocation) {
                  setState(() {
                    _centerMarkerLocation = newLocation;
                  });
                },
                onClose: () {
                  _toggleSplitScreenMode();
                },
                onSpotAdded: (spot) {
                  setState(() {
                    _spots.add(spot);
                  });
                  _toggleSplitScreenMode();
                },
              ),
            ),
          ],
        );
      },
    );
  }

  // 切换分屏模式
  void _toggleSplitScreenMode() {
    setState(() {
      _isSplitScreenMode = !_isSplitScreenMode;
      if (_isSplitScreenMode) {
        // 进入分屏模式时，设置中心标记位置为当前地图中心
        _centerMarkerLocation = mapController.camera.center;
        // 开始上拉动画
        _animationController.forward();
      } else {
        // 退出分屏模式时，重置动画
        _animationController.reverse();
        // 清除建议的钓点名称
        _suggestedSpotName = null;
      }
    });
  }

  // 切换分屏模式并获取位置名称
  Future<void> _toggleSplitScreenModeWithLocationName() async {
    if (_isSplitScreenMode) {
      // 如果已经在分屏模式，直接退出
      _toggleSplitScreenMode();
      return;
    }

    // 获取地图中心坐标
    final centerLocation = mapController.camera.center;

    setState(() {
      _isSplitScreenMode = true;
      _centerMarkerLocation = centerLocation;
      // 开始上拉动画
      _animationController.forward();
    });

    // 异步获取位置名称
    try {
      debugPrint('开始获取位置名称: ${centerLocation.latitude}, ${centerLocation.longitude}');

      final locationName = await Services.tianDiTu.getBestLocationName(
        centerLocation.longitude,
        centerLocation.latitude,
      );

      if (locationName != null && locationName.trim().isNotEmpty) {
        setState(() {
          _suggestedSpotName = '${locationName.trim()}钓点';
        });
        debugPrint('获取到位置名称: $_suggestedSpotName');
      } else {
        debugPrint('未获取到有效的位置名称');
        setState(() {
          _suggestedSpotName = null;
        });
      }
    } catch (e) {
      debugPrint('获取位置名称失败: $e');
      setState(() {
        _suggestedSpotName = null;
      });
    }
  }

  // 用于防止频繁加载的变量
  bool _isLoadingSpots = false;
  DateTime _lastLoadTime = DateTime.now();

  // 根据地图范围加载钓点
  Future<void> _loadSpotsInBounds() async {
    // 如果页面正在加载或者已经在加载钓点，则跳过
    if (_isLoading || _isLoadingSpots) return;

    // 防止频繁加载：如果距离上次加载不足500毫秒，则跳过
    final now = DateTime.now();
    if (now.difference(_lastLoadTime).inMilliseconds < 500) return;

    _isLoadingSpots = true;
    _lastLoadTime = now;

    final bounds = mapController.camera.visibleBounds;

    final minLat = bounds.south;
    final maxLat = bounds.north;
    final minLng = bounds.west;
    final maxLng = bounds.east;

    try {
      // 计算当前缩放级别下合适的钓点数量限制
      // 缩放级别越小（看到的区域越大），限制越大
      final zoomLevel = mapController.camera.zoom;
      final limit = zoomLevel < 10 ? 50 : (zoomLevel < 14 ? 30 : 20);

      final spots = await Services.fishingSpot.getSpotsInBounds(
        minLat: minLat,
        maxLat: maxLat,
        minLng: minLng,
        maxLng: maxLng,
        limit: limit,
      );

      if (mounted) {
        setState(() {
          _spots = spots;
        });
      }
    } catch (e) {
      debugPrint('加载范围内钓点失败: $e');
    } finally {
      _isLoadingSpots = false;
    }
  }

  // 更新当前位置并移动地图
  Future<void> _updateCurrentLocation() async {
    if (_isLocationResetting) return;

    setState(() {
      _isLocationResetting = true;
    });

    try {
      // 异步获取最新位置
      final newLocation = await Services.location.requestLocationUpdate();

      if (mounted) {
        setState(() {
          _userLocation = newLocation;
          _isLocationResetting = false;
        });

        // 保持当前缩放级别，移动地图到新位置
        final currentZoom = mapController.camera.zoom;
        mapController.move(_userLocation, currentZoom);

        // 显示成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('位置已更新'),
              ],
            ),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );

        // 加载新位置范围内的钓点
        _loadSpotsInBounds();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLocationResetting = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('获取位置失败: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // 显示钓点详情
  void _showSpotDetails(FishingSpot spot) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            height: MediaQuery.of(context).size.height * 0.6,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  spot.name,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text('分享者: ${spot.sharedBy}'),
                const SizedBox(height: 8),
                Text('创建时间: ${spot.createdAt.toString().substring(0, 10)}'),
                const SizedBox(height: 16),
                Text(spot.description, style: const TextStyle(fontSize: 16)),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Icon(Icons.thumb_up),
                    const SizedBox(width: 4),
                    Text('${spot.likes}'),
                    const SizedBox(width: 16),
                    const Icon(Icons.thumb_down),
                    const SizedBox(width: 4),
                    Text('${spot.unlikes}'),
                  ],
                ),
                const SizedBox(height: 16),
                const Text(
                  '评论',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child:
                      spot.comments.isEmpty
                          ? const Center(child: Text('暂无评论'))
                          : ListView.builder(
                            itemCount: spot.comments.length,
                            itemBuilder: (context, index) {
                              final comment = spot.comments[index];
                              return ListTile(
                                title: Text(comment.username),
                                subtitle: Text(comment.content),
                                trailing: Text(
                                  comment.createdAt.toString().substring(0, 10),
                                ),
                              );
                            },
                          ),
                ),
              ],
            ),
          ),
    );
  }
}
